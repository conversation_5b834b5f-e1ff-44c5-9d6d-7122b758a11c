"""
测试使用PyEMD库的新MEEMD实现
"""

import numpy as np
import matplotlib.pyplot as plt
import sys
from pathlib import Path
import time

# 添加src目录到路径
sys.path.append('src')

from meemd_2d import MEEMD2D, MEEMDConfig

# 设置中文显示
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def test_synthetic_data():
    """测试合成数据"""
    print("=" * 60)
    print("测试1: 合成数据的二维MEEMD分解")
    print("=" * 60)
    
    # 创建合成的二维数据
    lat_size, lon_size = 20, 30  # 较小的尺寸以加快测试
    x = np.linspace(0, 4*np.pi, lon_size)
    y = np.linspace(0, 2*np.pi, lat_size)
    X, Y = np.meshgrid(x, y)
    
    # 多尺度信号合成
    np.random.seed(42)
    large_scale = 3.0 * np.sin(X) * np.cos(Y)                    # 大尺度模式
    medium_scale = 1.5 * np.sin(2*X) * np.sin(1.5*Y)            # 中尺度模式  
    small_scale = 0.8 * np.sin(4*X) * np.cos(3*Y)               # 小尺度模式
    noise = 0.3 * np.random.randn(lat_size, lon_size)           # 噪声
    
    synthetic_data = large_scale + medium_scale + small_scale + noise
    
    print(f"合成数据形状: {synthetic_data.shape}")
    print(f"数据范围: {synthetic_data.min():.3f} 到 {synthetic_data.max():.3f}")
    
    # 配置MEEMD参数
    config = MEEMDConfig(
        trials=20,          # 减少试验次数以加快测试
        noise_std=0.2,
        S_number=4,
        num_siftings=50,
        parallel=False,     # 关闭并行以便调试
        random_seed=42
    )
    
    print(f"\nMEEMD配置:")
    print(f"  试验次数: {config.trials}")
    print(f"  噪声标准差: {config.noise_std}")
    print(f"  并行处理: {config.parallel}")
    
    # 执行分解
    meemd = MEEMD2D(config)
    
    start_time = time.time()
    try:
        result = meemd.decompose_2d(synthetic_data)
        end_time = time.time()
        
        print(f"\n分解结果:")
        print(f"最终分量数: {len(result['final_components'])}")
        print(f"重构误差: {result['reconstruction_error']:.6f}")
        print(f"处理时间: {end_time - start_time:.2f} 秒")
        
        # 分析分量
        analysis = meemd.analyze_components(result['final_components'])
        print(f"\n分量分析:")
        for i, (energy_ratio, spatial_scale) in enumerate(zip(analysis['energy_ratios'], analysis['spatial_scales'])):
            print(f"  分量{i+1}: 能量占比={energy_ratio:.3f}, 空间尺度={spatial_scale:.2f}")
        
        # 可视化结果
        visualize_results(synthetic_data, result, "synthetic_new")
        
        return result
        
    except Exception as e:
        print(f"分解过程出错: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_sst_data():
    """测试真实SST数据"""
    print("\n" + "=" * 60)
    print("测试2: 真实SST数据的二维MEEMD分解")
    print("=" * 60)
    
    # 检查数据文件
    if not Path("SST-V2.nc").exists():
        print("警告: SST-V2.nc 文件不存在，跳过真实数据测试")
        return None
    
    import xarray as xr
    
    # 加载数据
    print("加载SST数据...")
    ds = xr.open_dataset("SST-V2.nc")
    sst_data = ds['analysed_sst'].values
    
    print(f"数据形状: {sst_data.shape}")
    
    # 选择一个时间点和子区域
    t_idx = 0
    sst_slice = sst_data[t_idx, :, :]
    
    # 转换为摄氏度并选择子区域
    sst_celsius = sst_slice - 273.15
    sub_region = sst_celsius[30:50, 40:70]  # 20x30的子区域
    
    print(f"处理时间点 {t_idx}")
    print(f"子区域形状: {sub_region.shape}")
    print(f"温度范围: {sub_region.min():.2f}°C 到 {sub_region.max():.2f}°C")
    
    # 配置MEEMD参数（更保守的设置）
    config = MEEMDConfig(
        trials=15,          # 进一步减少试验次数
        noise_std=0.1,      # 减小噪声标准差
        S_number=4,
        num_siftings=30,    # 减少筛选次数
        parallel=False,
        random_seed=42
    )
    
    # 执行分解
    meemd = MEEMD2D(config)
    
    start_time = time.time()
    try:
        result = meemd.decompose_2d(sub_region)
        end_time = time.time()
        
        print(f"\n分解结果:")
        print(f"最终分量数: {len(result['final_components'])}")
        print(f"重构误差: {result['reconstruction_error']:.6f}")
        print(f"处理时间: {end_time - start_time:.2f} 秒")
        
        # 分析分量
        analysis = meemd.analyze_components(result['final_components'])
        print(f"\n分量分析:")
        for i, (energy_ratio, spatial_scale) in enumerate(zip(analysis['energy_ratios'], analysis['spatial_scales'])):
            print(f"  分量{i+1}: 能量占比={energy_ratio:.3f}, 空间尺度={spatial_scale:.2f}")
        
        # 可视化结果
        visualize_results(sub_region, result, f"sst_new_t{t_idx}")
        
        return result
        
    except Exception as e:
        print(f"分解过程出错: {e}")
        import traceback
        traceback.print_exc()
        return None
    
    finally:
        ds.close()

def visualize_results(original_data, meemd_result, prefix):
    """可视化分解结果"""
    components = meemd_result['final_components']
    num_components = len(components)
    
    # 计算子图布局
    cols = min(3, num_components + 1)
    rows = (num_components + 1 + cols - 1) // cols
    
    fig, axes = plt.subplots(rows, cols, figsize=(5*cols, 4*rows))
    if rows == 1:
        axes = axes.reshape(1, -1)
    axes = axes.flatten()
    
    # 显示原始数据
    im0 = axes[0].imshow(original_data, cmap='RdYlBu_r', aspect='auto')
    axes[0].set_title('原始数据', fontsize=12)
    axes[0].set_xlabel('经度索引')
    axes[0].set_ylabel('纬度索引')
    plt.colorbar(im0, ax=axes[0])
    
    # 显示各个分量
    for i, component in enumerate(components):
        if i + 1 < len(axes):
            im = axes[i + 1].imshow(component, cmap='RdBu_r', aspect='auto')
            axes[i + 1].set_title(f'MEEMD分量 {i + 1}', fontsize=12)
            axes[i + 1].set_xlabel('经度索引')
            axes[i + 1].set_ylabel('纬度索引')
            plt.colorbar(im, ax=axes[i + 1])
    
    # 隐藏多余的子图
    for i in range(num_components + 1, len(axes)):
        axes[i].set_visible(False)
    
    plt.tight_layout()
    
    # 保存图片
    output_dir = Path("results")
    output_dir.mkdir(exist_ok=True)
    plt.savefig(output_dir / f"{prefix}_decomposition.png", dpi=150, bbox_inches='tight')
    print(f"可视化结果已保存: {output_dir / f'{prefix}_decomposition.png'}")
    
    plt.show()

def main():
    """主函数"""
    print("使用PyEMD库的新MEEMD实现测试")
    print("=" * 80)
    
    # 创建结果目录
    Path("results").mkdir(exist_ok=True)
    
    try:
        # 测试1: 合成数据
        synthetic_result = test_synthetic_data()
        
        # 测试2: 真实SST数据
        sst_result = test_sst_data()
        
        print("\n" + "=" * 80)
        print("所有测试完成！")
        
        if synthetic_result is not None:
            print("✓ 合成数据测试成功")
        else:
            print("✗ 合成数据测试失败")
            
        if sst_result is not None:
            print("✓ SST数据测试成功")
        else:
            print("✗ SST数据测试失败或跳过")
        
        print("请查看 results/ 目录中的可视化结果")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
