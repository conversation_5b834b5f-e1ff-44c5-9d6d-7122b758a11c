"""
二维MEEMD分解算法实现
用于对单个时间点的二维SST数据进行多尺度分解

基于"逐维切片分解，再按尺度组合"的MEEMD核心思想：
1. 第一维度（纬度方向）EEMD分解
2. 第二维度（经度方向）EEMD分解  
3. 基于可比最小尺度原则重组分量
"""

import numpy as np
import warnings
from typing import List, Tuple, Optional
from dataclasses import dataclass
from scipy.interpolate import interp1d

warnings.filterwarnings('ignore')

@dataclass
class MEEMDConfig:
    """MEEMD配置参数"""
    trials: int = 50           # EEMD集成次数
    noise_std: float = 0.2     # 噪声标准差
    max_imfs: int = 8          # 最大IMF数量

class SimpleEEMD:
    """简化的EEMD实现"""

    def __init__(self, trials=50, noise_std=0.2, max_imfs=8):
        self.trials = trials
        self.noise_std = noise_std
        self.max_imfs = max_imfs

    def eemd(self, signal):
        """执行EEMD分解"""
        if len(signal) < 4:
            return [signal]

        # 集成多次EMD结果
        all_imfs = []

        for trial in range(self.trials):
            # 添加白噪声
            noise = np.random.normal(0, self.noise_std * np.std(signal), len(signal))
            noisy_signal = signal + noise

            # 执行EMD
            imfs = self._emd(noisy_signal)
            all_imfs.append(imfs)

        # 平均所有试验的结果
        max_imf_count = max(len(imfs) for imfs in all_imfs)
        averaged_imfs = []

        for i in range(max_imf_count):
            imf_sum = np.zeros_like(signal)
            count = 0

            for imfs in all_imfs:
                if i < len(imfs):
                    imf_sum += imfs[i]
                    count += 1

            if count > 0:
                averaged_imfs.append(imf_sum / count)

        return averaged_imfs[:self.max_imfs]

    def _emd(self, signal):
        """简化的EMD实现"""
        imfs = []
        residue = signal.copy()

        for _ in range(self.max_imfs):
            imf = self._extract_imf(residue)
            if imf is None:
                break

            imfs.append(imf)
            residue = residue - imf

            # 停止条件：残差变化很小
            if np.std(residue) < 0.01 * np.std(signal):
                break

        # 添加最终残差
        if len(residue) > 0:
            imfs.append(residue)

        return imfs

    def _extract_imf(self, signal):
        """提取单个IMF"""
        h = signal.copy()

        for sift in range(50):  # 最大筛选次数
            # 找到极值点
            maxima_idx, minima_idx = self._find_extrema(h)

            if len(maxima_idx) < 2 or len(minima_idx) < 2:
                return None

            # 构造上下包络
            try:
                upper_env = self._interpolate_envelope(maxima_idx, h[maxima_idx], len(h))
                lower_env = self._interpolate_envelope(minima_idx, h[minima_idx], len(h))

                # 计算均值包络
                mean_env = (upper_env + lower_env) / 2

                # 更新h
                h_new = h - mean_env

                # 检查停止条件
                if self._is_imf(h_new):
                    return h_new

                h = h_new

            except:
                return None

        return h

    def _find_extrema(self, signal):
        """找到极值点"""
        maxima = []
        minima = []

        for i in range(1, len(signal) - 1):
            if signal[i] > signal[i-1] and signal[i] > signal[i+1]:
                maxima.append(i)
            elif signal[i] < signal[i-1] and signal[i] < signal[i+1]:
                minima.append(i)

        return np.array(maxima), np.array(minima)

    def _interpolate_envelope(self, extrema_idx, extrema_val, signal_len):
        """插值包络线"""
        if len(extrema_idx) < 2:
            return np.zeros(signal_len)

        # 边界处理
        x = np.concatenate([[0], extrema_idx, [signal_len-1]])
        y = np.concatenate([[extrema_val[0]], extrema_val, [extrema_val[-1]]])

        # 三次样条插值
        f = interp1d(x, y, kind='cubic', bounds_error=False, fill_value='extrapolate')
        return f(np.arange(signal_len))

    def _is_imf(self, signal):
        """检查是否为IMF"""
        maxima_idx, minima_idx = self._find_extrema(signal)

        # IMF条件：极值点数量相差不超过1
        if abs(len(maxima_idx) - len(minima_idx)) > 1:
            return False

        # 均值接近零
        if len(maxima_idx) > 0 and len(minima_idx) > 0:
            mean_val = np.mean(signal)
            return abs(mean_val) < 0.1 * np.std(signal)

        return True

class MEEMD2D:
    """二维MEEMD分解器"""

    def __init__(self, config: MEEMDConfig = None):
        self.config = config or MEEMDConfig()
        self.eemd = SimpleEEMD(
            trials=self.config.trials,
            noise_std=self.config.noise_std,
            max_imfs=self.config.max_imfs
        )
        
    def decompose_2d(self, data_2d: np.ndarray) -> dict:
        """
        对二维数据进行MEEMD分解
        
        Args:
            data_2d: 二维数组 (lat, lon)
            
        Returns:
            dict: 包含分解结果的字典
                - 'intermediate_components': 中间分量矩阵 h_{j,k}
                - 'final_components': 最终重组分量 C_i
                - 'residue': 残差项
                - 'reconstruction_error': 重构误差
        """
        print(f"开始二维MEEMD分解，数据形状: {data_2d.shape}")
        
        # 步骤1: 第一维度（纬度方向）分解
        print("步骤1: 纬度方向EEMD分解...")
        first_dim_components = self._decompose_first_dimension(data_2d)
        
        # 步骤2: 第二维度（经度方向）分解
        print("步骤2: 经度方向EEMD分解...")
        intermediate_matrix = self._decompose_second_dimension(first_dim_components)
        
        # 步骤3: 基于可比最小尺度原则重组
        print("步骤3: 可比最小尺度重组...")
        final_components = self._recombine_components(intermediate_matrix)
        
        # 计算重构误差
        reconstruction = self._reconstruct_from_components(final_components)
        reconstruction_error = np.mean((data_2d - reconstruction) ** 2)
        
        return {
            'intermediate_components': intermediate_matrix,
            'final_components': final_components,
            'residue': final_components[-1],
            'reconstruction_error': reconstruction_error,
            'original_shape': data_2d.shape
        }
    
    def _decompose_first_dimension(self, data_2d: np.ndarray) -> List[np.ndarray]:
        """第一维度（纬度方向）EEMD分解"""
        lat_size, lon_size = data_2d.shape
        first_dim_components = []
        
        # 对每一行（固定纬度）进行EEMD分解
        all_row_imfs = []
        max_imfs = 0
        
        for lat_idx in range(lat_size):
            row_signal = data_2d[lat_idx, :]
            try:
                # 对该行进行EEMD分解
                imfs = self.eemd.eemd(row_signal)
                all_row_imfs.append(imfs)
                max_imfs = max(max_imfs, len(imfs))
            except Exception as e:
                print(f"警告: 第{lat_idx}行分解失败: {e}")
                # 使用原始信号作为单一分量
                all_row_imfs.append([row_signal])
                max_imfs = max(max_imfs, 1)
        
        # 重新组织IMF分量，形成二维分量场 g_j
        for imf_idx in range(max_imfs):
            component_2d = np.zeros((lat_size, lon_size))
            
            for lat_idx in range(lat_size):
                if imf_idx < len(all_row_imfs[lat_idx]):
                    component_2d[lat_idx, :] = all_row_imfs[lat_idx][imf_idx]
                else:
                    # 如果该行的IMF数量不足，用零填充
                    component_2d[lat_idx, :] = 0
                    
            first_dim_components.append(component_2d)
            
        print(f"第一维度分解完成，得到{len(first_dim_components)}个分量")
        return first_dim_components
    
    def _decompose_second_dimension(self, first_dim_components: List[np.ndarray]) -> np.ndarray:
        """第二维度（经度方向）分解"""
        lat_size, lon_size = first_dim_components[0].shape
        num_first_components = len(first_dim_components)
        
        # 存储中间分量矩阵 h_{j,k}
        intermediate_matrix = []
        
        for j, g_component in enumerate(first_dim_components):
            print(f"  处理第{j+1}/{num_first_components}个第一维分量...")
            
            # 对每一列（固定经度）进行EEMD分解
            all_col_imfs = []
            max_imfs = 0
            
            for lon_idx in range(lon_size):
                col_signal = g_component[:, lon_idx]
                try:
                    # 对该列进行EEMD分解
                    imfs = self.eemd.eemd(col_signal)
                    all_col_imfs.append(imfs)
                    max_imfs = max(max_imfs, len(imfs))
                except Exception as e:
                    print(f"    警告: 第{lon_idx}列分解失败: {e}")
                    all_col_imfs.append([col_signal])
                    max_imfs = max(max_imfs, 1)
            
            # 重新组织该g_component的所有IMF分量
            g_components = []
            for imf_idx in range(max_imfs):
                component_2d = np.zeros((lat_size, lon_size))
                
                for lon_idx in range(lon_size):
                    if imf_idx < len(all_col_imfs[lon_idx]):
                        component_2d[:, lon_idx] = all_col_imfs[lon_idx][imf_idx]
                    else:
                        component_2d[:, lon_idx] = 0
                        
                g_components.append(component_2d)
            
            intermediate_matrix.append(g_components)
        
        print(f"第二维度分解完成，中间矩阵形状: {len(intermediate_matrix)} x {len(intermediate_matrix[0])}")
        return intermediate_matrix
    
    def _recombine_components(self, intermediate_matrix: List[List[np.ndarray]]) -> List[np.ndarray]:
        """基于可比最小尺度原则重组分量"""
        if not intermediate_matrix or not intermediate_matrix[0]:
            return []
            
        lat_size, lon_size = intermediate_matrix[0][0].shape
        
        # 确定矩阵维度
        num_rows = len(intermediate_matrix)  # 第一维分量数
        num_cols = len(intermediate_matrix[0])  # 第二维分量数
        
        print(f"中间矩阵维度: {num_rows} x {num_cols}")
        
        # 按照可比最小尺度原则重组
        final_components = []
        max_components = min(num_rows, num_cols)
        
        for i in range(max_components):
            combined_component = np.zeros((lat_size, lon_size))
            component_count = 0
            
            # 添加第i行的分量（从对角线开始）
            for k in range(i, num_cols):
                if k < len(intermediate_matrix[i]):
                    combined_component += intermediate_matrix[i][k]
                    component_count += 1
            
            # 添加第i列的分量（对角线以下）
            for j in range(i + 1, num_rows):
                if i < len(intermediate_matrix[j]):
                    combined_component += intermediate_matrix[j][i]
                    component_count += 1
            
            if component_count > 0:
                final_components.append(combined_component)
        
        print(f"最终重组得到{len(final_components)}个分量")
        return final_components
    
    def _reconstruct_from_components(self, components: List[np.ndarray]) -> np.ndarray:
        """从分量重构原始数据"""
        if not components:
            return np.array([])
            
        reconstruction = np.zeros_like(components[0])
        for component in components:
            reconstruction += component
            
        return reconstruction
    
    def analyze_components(self, components: List[np.ndarray]) -> dict:
        """分析分量特性"""
        analysis = {
            'num_components': len(components),
            'component_energies': [],
            'component_variances': [],
            'spatial_scales': []
        }
        
        total_energy = 0
        for i, comp in enumerate(components):
            energy = np.sum(comp ** 2)
            variance = np.var(comp)
            
            analysis['component_energies'].append(energy)
            analysis['component_variances'].append(variance)
            total_energy += energy
            
            # 简单的空间尺度估计（基于梯度）
            grad_x = np.gradient(comp, axis=0)
            grad_y = np.gradient(comp, axis=1)
            spatial_scale = 1.0 / (np.mean(np.sqrt(grad_x**2 + grad_y**2)) + 1e-10)
            analysis['spatial_scales'].append(spatial_scale)
        
        # 计算能量占比
        analysis['energy_ratios'] = [e/total_energy for e in analysis['component_energies']]
        
        return analysis

def test_meemd_2d():
    """测试二维MEEMD分解"""
    # 创建测试数据
    lat_size, lon_size = 50, 60
    x = np.linspace(0, 4*np.pi, lon_size)
    y = np.linspace(0, 2*np.pi, lat_size)
    X, Y = np.meshgrid(x, y)
    
    # 合成多尺度信号
    data_2d = (np.sin(X) * np.cos(Y) +           # 大尺度
               0.5 * np.sin(3*X) * np.sin(2*Y) + # 中尺度
               0.2 * np.sin(8*X) * np.cos(5*Y) + # 小尺度
               0.1 * np.random.randn(lat_size, lon_size))  # 噪声
    
    # 执行分解
    config = MEEMDConfig(trials=50, parallel=False)  # 减少计算时间
    meemd = MEEMD2D(config)
    
    result = meemd.decompose_2d(data_2d)
    
    print(f"\n分解结果:")
    print(f"最终分量数: {len(result['final_components'])}")
    print(f"重构误差: {result['reconstruction_error']:.6f}")
    
    # 分析分量特性
    analysis = meemd.analyze_components(result['final_components'])
    print(f"\n分量分析:")
    for i, (energy_ratio, spatial_scale) in enumerate(zip(analysis['energy_ratios'], analysis['spatial_scales'])):
        print(f"分量{i+1}: 能量占比={energy_ratio:.3f}, 空间尺度={spatial_scale:.2f}")

if __name__ == "__main__":
    test_meemd_2d()
