"""
二维MEEMD分解算法实现
用于对单个时间点的二维SST数据进行多尺度分解

基于"逐维切片分解，再按尺度组合"的MEEMD核心思想：
1. 第一维度（纬度方向）EEMD分解
2. 第二维度（经度方向）EEMD分解
3. 基于可比最小尺度原则重组分量
"""

import numpy as np
from PyEMD import EEMD
import warnings
from typing import List, Tuple, Optional
from dataclasses import dataclass
import time

warnings.filterwarnings('ignore')

@dataclass
class MEEMDConfig:
    """MEEMD配置参数"""
    trials: int = 100          # EEMD集成次数
    noise_std: float = 0.2     # 噪声标准差
    S_number: int = 4          # 停止准则参数
    num_siftings: int = 50     # 最大筛选次数
    parallel: bool = True      # 是否并行处理
    random_seed: int = 42      # 随机种子，确保结果可重复

class MEEMD2D:
    """二维MEEMD分解器"""

    def __init__(self, config: MEEMDConfig = None):
        self.config = config or MEEMDConfig()

        # 设置随机种子确保结果可重复
        np.random.seed(self.config.random_seed)

        # 初始化EEMD分解器
        self.eemd = EEMD(
            trials=self.config.trials,
            noise_std=self.config.noise_std,
            S_number=self.config.S_number,
            num_siftings=self.config.num_siftings,
            parallel=self.config.parallel
        )
        
    def decompose_2d(self, data_2d: np.ndarray) -> dict:
        """
        对二维数据进行MEEMD分解
        
        Args:
            data_2d: 二维数组 (lat, lon)
            
        Returns:
            dict: 包含分解结果的字典
                - 'intermediate_components': 中间分量矩阵 h_{j,k}
                - 'final_components': 最终重组分量 C_i
                - 'residue': 残差项
                - 'reconstruction_error': 重构误差
        """
        print(f"开始二维MEEMD分解，数据形状: {data_2d.shape}")
        
        # 步骤1: 第一维度（纬度方向）分解
        print("步骤1: 纬度方向EEMD分解...")
        first_dim_components = self._decompose_first_dimension(data_2d)
        
        # 步骤2: 第二维度（经度方向）分解
        print("步骤2: 经度方向EEMD分解...")
        intermediate_matrix = self._decompose_second_dimension(first_dim_components)
        
        # 步骤3: 基于可比最小尺度原则重组
        print("步骤3: 可比最小尺度重组...")
        final_components = self._recombine_components(intermediate_matrix)
        
        # 计算重构误差
        reconstruction = self._reconstruct_from_components(final_components)
        reconstruction_error = np.mean((data_2d - reconstruction) ** 2)
        
        return {
            'intermediate_components': intermediate_matrix,
            'final_components': final_components,
            'residue': final_components[-1],
            'reconstruction_error': reconstruction_error,
            'original_shape': data_2d.shape
        }
    
    def _decompose_first_dimension(self, data_2d: np.ndarray) -> List[np.ndarray]:
        """第一维度（纬度方向）EEMD分解"""
        lat_size, lon_size = data_2d.shape
        first_dim_components = []

        print(f"  对{lat_size}行数据进行EEMD分解...")

        # 对每一行（固定纬度）进行EEMD分解
        all_row_imfs = []
        max_imfs = 0

        for lat_idx in range(lat_size):
            row_signal = data_2d[lat_idx, :]

            # 检查信号是否有效（非常数、非NaN）
            if len(np.unique(row_signal)) < 3 or np.any(np.isnan(row_signal)):
                print(f"    警告: 第{lat_idx}行信号无效，跳过分解")
                all_row_imfs.append([row_signal])
                max_imfs = max(max_imfs, 1)
                continue

            try:
                # 对该行进行EEMD分解
                imfs = self.eemd.eemd(row_signal)
                all_row_imfs.append(imfs)
                max_imfs = max(max_imfs, len(imfs))

                if lat_idx % 10 == 0:  # 每10行打印一次进度
                    print(f"    已完成第{lat_idx+1}/{lat_size}行，得到{len(imfs)}个IMF")

            except Exception as e:
                print(f"    警告: 第{lat_idx}行分解失败: {e}")
                # 使用原始信号作为单一分量
                all_row_imfs.append([row_signal])
                max_imfs = max(max_imfs, 1)

        # 重新组织IMF分量，形成二维分量场 g_j
        print(f"  重新组织IMF分量，最大IMF数量: {max_imfs}")
        for imf_idx in range(max_imfs):
            component_2d = np.zeros((lat_size, lon_size))

            for lat_idx in range(lat_size):
                if imf_idx < len(all_row_imfs[lat_idx]):
                    component_2d[lat_idx, :] = all_row_imfs[lat_idx][imf_idx]
                else:
                    # 如果该行的IMF数量不足，用最后一个分量填充
                    if len(all_row_imfs[lat_idx]) > 0:
                        component_2d[lat_idx, :] = all_row_imfs[lat_idx][-1]
                    else:
                        component_2d[lat_idx, :] = 0

            first_dim_components.append(component_2d)

        print(f"第一维度分解完成，得到{len(first_dim_components)}个分量")
        return first_dim_components
    
    def _decompose_second_dimension(self, first_dim_components: List[np.ndarray]) -> np.ndarray:
        """第二维度（经度方向）分解"""
        lat_size, lon_size = first_dim_components[0].shape
        num_first_components = len(first_dim_components)

        # 存储中间分量矩阵 h_{j,k}
        intermediate_matrix = []

        for j, g_component in enumerate(first_dim_components):
            print(f"  处理第{j+1}/{num_first_components}个第一维分量...")
            print(f"    对{lon_size}列数据进行EEMD分解...")

            # 对每一列（固定经度）进行EEMD分解
            all_col_imfs = []
            max_imfs = 0

            for lon_idx in range(lon_size):
                col_signal = g_component[:, lon_idx]

                # 检查信号是否有效
                if len(np.unique(col_signal)) < 3 or np.any(np.isnan(col_signal)):
                    all_col_imfs.append([col_signal])
                    max_imfs = max(max_imfs, 1)
                    continue

                try:
                    # 对该列进行EEMD分解
                    imfs = self.eemd.eemd(col_signal)
                    all_col_imfs.append(imfs)
                    max_imfs = max(max_imfs, len(imfs))

                    if lon_idx % 20 == 0:  # 每20列打印一次进度
                        print(f"      已完成第{lon_idx+1}/{lon_size}列，得到{len(imfs)}个IMF")

                except Exception as e:
                    print(f"      警告: 第{lon_idx}列分解失败: {e}")
                    all_col_imfs.append([col_signal])
                    max_imfs = max(max_imfs, 1)

            # 重新组织该g_component的所有IMF分量
            print(f"    重新组织IMF分量，最大IMF数量: {max_imfs}")
            g_components = []
            for imf_idx in range(max_imfs):
                component_2d = np.zeros((lat_size, lon_size))

                for lon_idx in range(lon_size):
                    if imf_idx < len(all_col_imfs[lon_idx]):
                        component_2d[:, lon_idx] = all_col_imfs[lon_idx][imf_idx]
                    else:
                        # 如果该列的IMF数量不足，用最后一个分量填充
                        if len(all_col_imfs[lon_idx]) > 0:
                            component_2d[:, lon_idx] = all_col_imfs[lon_idx][-1]
                        else:
                            component_2d[:, lon_idx] = 0

                g_components.append(component_2d)

            intermediate_matrix.append(g_components)

        print(f"第二维度分解完成，中间矩阵形状: {len(intermediate_matrix)} x {len(intermediate_matrix[0])}")
        return intermediate_matrix
    
    def _recombine_components(self, intermediate_matrix: List[List[np.ndarray]]) -> List[np.ndarray]:
        """基于可比最小尺度原则重组分量"""
        if not intermediate_matrix or not intermediate_matrix[0]:
            return []
            
        lat_size, lon_size = intermediate_matrix[0][0].shape
        
        # 确定矩阵维度
        num_rows = len(intermediate_matrix)  # 第一维分量数
        num_cols = len(intermediate_matrix[0])  # 第二维分量数
        
        print(f"中间矩阵维度: {num_rows} x {num_cols}")
        
        # 按照可比最小尺度原则重组
        final_components = []
        max_components = min(num_rows, num_cols)
        
        for i in range(max_components):
            combined_component = np.zeros((lat_size, lon_size))
            component_count = 0
            
            # 添加第i行的分量（从对角线开始）
            for k in range(i, num_cols):
                if k < len(intermediate_matrix[i]):
                    combined_component += intermediate_matrix[i][k]
                    component_count += 1
            
            # 添加第i列的分量（对角线以下）
            for j in range(i + 1, num_rows):
                if i < len(intermediate_matrix[j]):
                    combined_component += intermediate_matrix[j][i]
                    component_count += 1
            
            if component_count > 0:
                final_components.append(combined_component)
        
        print(f"最终重组得到{len(final_components)}个分量")
        return final_components
    
    def _reconstruct_from_components(self, components: List[np.ndarray]) -> np.ndarray:
        """从分量重构原始数据"""
        if not components:
            return np.array([])
            
        reconstruction = np.zeros_like(components[0])
        for component in components:
            reconstruction += component
            
        return reconstruction
    
    def analyze_components(self, components: List[np.ndarray]) -> dict:
        """分析分量特性"""
        analysis = {
            'num_components': len(components),
            'component_energies': [],
            'component_variances': [],
            'spatial_scales': []
        }
        
        total_energy = 0
        for i, comp in enumerate(components):
            energy = np.sum(comp ** 2)
            variance = np.var(comp)
            
            analysis['component_energies'].append(energy)
            analysis['component_variances'].append(variance)
            total_energy += energy
            
            # 简单的空间尺度估计（基于梯度）
            grad_x = np.gradient(comp, axis=0)
            grad_y = np.gradient(comp, axis=1)
            spatial_scale = 1.0 / (np.mean(np.sqrt(grad_x**2 + grad_y**2)) + 1e-10)
            analysis['spatial_scales'].append(spatial_scale)
        
        # 计算能量占比
        analysis['energy_ratios'] = [e/total_energy for e in analysis['component_energies']]
        
        return analysis

def test_meemd_2d():
    """测试二维MEEMD分解"""
    print("=" * 60)
    print("二维MEEMD分解测试")
    print("=" * 60)

    # 创建测试数据
    lat_size, lon_size = 30, 40  # 减小尺寸以加快测试
    x = np.linspace(0, 4*np.pi, lon_size)
    y = np.linspace(0, 2*np.pi, lat_size)
    X, Y = np.meshgrid(x, y)

    # 合成多尺度信号
    np.random.seed(42)  # 确保结果可重复
    data_2d = (2.0 * np.sin(X) * np.cos(Y) +           # 大尺度
               1.0 * np.sin(3*X) * np.sin(2*Y) +        # 中尺度
               0.5 * np.sin(8*X) * np.cos(5*Y) +        # 小尺度
               0.2 * np.random.randn(lat_size, lon_size))  # 噪声

    print(f"测试数据形状: {data_2d.shape}")
    print(f"数据范围: {data_2d.min():.3f} 到 {data_2d.max():.3f}")

    # 执行分解
    config = MEEMDConfig(
        trials=30,          # 减少试验次数以加快测试
        noise_std=0.2,
        S_number=4,
        num_siftings=50,
        parallel=False,     # 关闭并行以便调试
        random_seed=42
    )

    print(f"\nMEEMD配置:")
    print(f"  试验次数: {config.trials}")
    print(f"  噪声标准差: {config.noise_std}")
    print(f"  并行处理: {config.parallel}")

    meemd = MEEMD2D(config)

    start_time = time.time()
    result = meemd.decompose_2d(data_2d)
    end_time = time.time()

    print(f"\n分解结果:")
    print(f"最终分量数: {len(result['final_components'])}")
    print(f"重构误差: {result['reconstruction_error']:.6f}")
    print(f"处理时间: {end_time - start_time:.2f} 秒")

    # 分析分量特性
    analysis = meemd.analyze_components(result['final_components'])
    print(f"\n分量分析:")
    for i, (energy_ratio, spatial_scale) in enumerate(zip(analysis['energy_ratios'], analysis['spatial_scales'])):
        print(f"  分量{i+1}: 能量占比={energy_ratio:.3f}, 空间尺度={spatial_scale:.2f}")

    return result

if __name__ == "__main__":
    test_meemd_2d()
