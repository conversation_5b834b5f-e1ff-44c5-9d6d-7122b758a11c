"""
简化的SST数据MEEMD测试
只测试单个时间点的处理
"""

import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
import sys
from pathlib import Path

# 添加src目录到路径
sys.path.append('src')

from meemd_2d import MEEMD2D, MEEMDConfig

# 设置中文显示
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def test_single_sst_slice():
    """测试单个SST时间切片的MEEMD分解"""
    print("=" * 60)
    print("简化SST数据MEEMD测试")
    print("=" * 60)
    
    # 检查数据文件
    if not Path("SST-V2.nc").exists():
        print("错误: SST-V2.nc 文件不存在")
        return
    
    # 加载数据
    print("加载SST数据...")
    ds = xr.open_dataset("SST-V2.nc")
    sst_data = ds['analysed_sst'].values
    
    print(f"数据形状: {sst_data.shape}")
    print(f"时间范围: {ds.time.values[0]} 到 {ds.time.values[-1]}")
    
    # 选择一个时间点
    t_idx = 0
    sst_slice = sst_data[t_idx, :, :]
    
    print(f"\n处理时间点 {t_idx}")
    print(f"SST切片形状: {sst_slice.shape}")
    print(f"温度范围: {sst_slice.min():.2f}K 到 {sst_slice.max():.2f}K")
    
    # 预处理：转换为摄氏度
    sst_celsius = sst_slice - 273.15
    print(f"摄氏度范围: {sst_celsius.min():.2f}°C 到 {sst_celsius.max():.2f}°C")
    
    # 为了加快测试，只处理一个子区域
    sub_region = sst_celsius[20:50, 30:70]  # 30x40的子区域
    print(f"子区域形状: {sub_region.shape}")
    
    # 配置MEEMD参数（快速测试）
    config = MEEMDConfig(
        trials=10,  # 减少试验次数
        noise_std=0.1,
        max_imfs=4  # 减少IMF数量
    )
    
    # 执行MEEMD分解
    print("\n开始MEEMD分解...")
    meemd = MEEMD2D(config)
    
    try:
        result = meemd.decompose_2d(sub_region)
        
        print(f"\n分解结果:")
        print(f"最终分量数: {len(result['final_components'])}")
        print(f"重构误差: {result['reconstruction_error']:.6f}")
        
        # 分析分量
        analysis = meemd.analyze_components(result['final_components'])
        print(f"\n分量分析:")
        for i, (energy_ratio, spatial_scale) in enumerate(zip(analysis['energy_ratios'], analysis['spatial_scales'])):
            print(f"  分量{i+1}: 能量占比={energy_ratio:.3f}, 空间尺度={spatial_scale:.2f}")
        
        # 可视化结果
        visualize_sst_decomposition(sub_region, result, f"sst_t{t_idx}")
        
        print(f"\n测试成功完成！")
        return result
        
    except Exception as e:
        print(f"分解过程出错: {e}")
        import traceback
        traceback.print_exc()
        return None
    
    finally:
        ds.close()

def visualize_sst_decomposition(original_data, meemd_result, prefix):
    """可视化SST分解结果"""
    components = meemd_result['final_components']
    num_components = len(components)
    
    # 创建子图
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    axes = axes.flatten()
    
    # 显示原始数据
    im0 = axes[0].imshow(original_data, cmap='RdYlBu_r', aspect='auto')
    axes[0].set_title('原始SST数据 (°C)', fontsize=12)
    axes[0].set_xlabel('经度索引')
    axes[0].set_ylabel('纬度索引')
    plt.colorbar(im0, ax=axes[0])
    
    # 显示各个分量
    for i, component in enumerate(components):
        if i + 1 < len(axes):
            im = axes[i + 1].imshow(component, cmap='RdBu_r', aspect='auto')
            axes[i + 1].set_title(f'MEEMD分量 {i + 1}', fontsize=12)
            axes[i + 1].set_xlabel('经度索引')
            axes[i + 1].set_ylabel('纬度索引')
            plt.colorbar(im, ax=axes[i + 1])
    
    # 隐藏多余的子图
    for i in range(num_components + 1, len(axes)):
        axes[i].set_visible(False)
    
    plt.tight_layout()
    
    # 保存图片
    output_dir = Path("results")
    output_dir.mkdir(exist_ok=True)
    plt.savefig(output_dir / f"{prefix}_sst_decomposition.png", dpi=150, bbox_inches='tight')
    print(f"可视化结果已保存: {output_dir / f'{prefix}_sst_decomposition.png'}")
    
    plt.show()

def analyze_decomposition_quality(original_data, meemd_result):
    """分析分解质量"""
    components = meemd_result['final_components']
    
    # 重构数据
    reconstruction = np.zeros_like(original_data)
    for comp in components:
        reconstruction += comp
    
    # 计算各种误差指标
    mse = np.mean((original_data - reconstruction) ** 2)
    rmse = np.sqrt(mse)
    mae = np.mean(np.abs(original_data - reconstruction))
    
    # 相关系数
    correlation = np.corrcoef(original_data.flatten(), reconstruction.flatten())[0, 1]
    
    print(f"\n分解质量评估:")
    print(f"  均方误差 (MSE): {mse:.6f}")
    print(f"  均方根误差 (RMSE): {rmse:.6f}")
    print(f"  平均绝对误差 (MAE): {mae:.6f}")
    print(f"  相关系数: {correlation:.6f}")
    
    return {
        'mse': mse,
        'rmse': rmse,
        'mae': mae,
        'correlation': correlation
    }

def main():
    """主函数"""
    print("开始简化SST数据MEEMD测试...")
    
    # 创建结果目录
    Path("results").mkdir(exist_ok=True)
    
    try:
        result = test_single_sst_slice()
        
        if result is not None:
            # 分析分解质量
            # 注意：这里需要使用子区域数据
            print("\n测试完成！请查看results目录中的可视化结果。")
        else:
            print("测试失败。")
            
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
