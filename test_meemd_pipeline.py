"""
测试MEEMD处理管道
验证逐时间点的二维MEEMD分解功能
"""

import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import sys

# 添加src目录到路径
sys.path.append('src')

from meemd_2d import MEEMD2D, MEEMDConfig
from data_processor import SSTDataProcessor, ProcessingConfig

# 设置中文显示
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def test_synthetic_data():
    """测试合成数据的二维MEEMD分解"""
    print("=" * 50)
    print("测试1: 合成数据的二维MEEMD分解")
    print("=" * 50)
    
    # 创建合成的二维SST数据
    lat_size, lon_size = 30, 40
    x = np.linspace(0, 4*np.pi, lon_size)
    y = np.linspace(0, 2*np.pi, lat_size)
    X, Y = np.meshgrid(x, y)
    
    # 多尺度信号合成
    large_scale = 2.0 * np.sin(X) * np.cos(Y)                    # 大尺度模式
    medium_scale = 1.0 * np.sin(2*X) * np.sin(1.5*Y)            # 中尺度模式  
    small_scale = 0.5 * np.sin(5*X) * np.cos(4*Y)               # 小尺度模式
    noise = 0.2 * np.random.randn(lat_size, lon_size)           # 噪声
    
    synthetic_sst = large_scale + medium_scale + small_scale + noise
    
    print(f"合成数据形状: {synthetic_sst.shape}")
    print(f"数据范围: {synthetic_sst.min():.3f} 到 {synthetic_sst.max():.3f}")
    
    # 配置MEEMD参数（减少计算时间）
    config = MEEMDConfig(
        trials=30,
        noise_std=0.2,
        max_imfs=6
    )
    
    # 执行分解
    meemd = MEEMD2D(config)
    result = meemd.decompose_2d(synthetic_sst)
    
    print(f"\n分解结果:")
    print(f"最终分量数: {len(result['final_components'])}")
    print(f"重构误差: {result['reconstruction_error']:.6f}")
    
    # 分析分量
    analysis = meemd.analyze_components(result['final_components'])
    print(f"\n分量分析:")
    for i, (energy_ratio, spatial_scale) in enumerate(zip(analysis['energy_ratios'], analysis['spatial_scales'])):
        print(f"  分量{i+1}: 能量占比={energy_ratio:.3f}, 空间尺度={spatial_scale:.2f}")
    
    # 可视化结果
    visualize_decomposition_results(synthetic_sst, result, "synthetic_data")
    
    return result

def test_real_sst_data():
    """测试真实SST数据的处理"""
    print("\n" + "=" * 50)
    print("测试2: 真实SST数据处理")
    print("=" * 50)
    
    # 检查数据文件是否存在
    if not Path("SST-V2.nc").exists():
        print("警告: SST-V2.nc 文件不存在，跳过真实数据测试")
        return None
    
    # 配置处理参数
    processing_config = ProcessingConfig(
        batch_size=2,
        start_time_idx=0,
        end_time_idx=3,  # 只处理前3个时间点
        save_intermediate=True,
        overwrite_cache=True
    )
    
    meemd_config = MEEMDConfig(
        trials=20,  # 减少计算时间
        noise_std=0.1,
        max_imfs=6
    )
    
    # 创建处理器
    processor = SSTDataProcessor(processing_config, meemd_config)
    
    # 加载数据
    if not processor.load_data():
        print("数据加载失败")
        return None
    
    print(f"数据加载成功，形状: {processor.sst_data.shape}")
    
    # 处理单个时间点作为示例
    t_idx = 0
    sst_slice = processor.sst_data[t_idx, :, :]
    timestamp = processor.time_coords[t_idx]
    
    print(f"\n处理时间点 {t_idx}: {timestamp}")
    print(f"SST切片形状: {sst_slice.shape}")
    print(f"温度范围: {sst_slice.min():.2f}K 到 {sst_slice.max():.2f}K")
    
    # 执行处理
    result = processor.process_single_time_slice(t_idx, sst_slice, timestamp)
    
    if result is not None:
        meemd_result = result['meemd_result']
        print(f"\n分解结果:")
        print(f"最终分量数: {len(meemd_result['final_components'])}")
        print(f"重构误差: {meemd_result['reconstruction_error']:.6f}")
        
        # 分析分量
        analysis = result['component_analysis']
        print(f"\n分量分析:")
        for i, (energy_ratio, spatial_scale) in enumerate(zip(analysis['energy_ratios'], analysis['spatial_scales'])):
            print(f"  分量{i+1}: 能量占比={energy_ratio:.3f}, 空间尺度={spatial_scale:.2f}")
        
        # 可视化结果
        visualize_decomposition_results(result['processed_data'], meemd_result, f"real_sst_t{t_idx}")
        
        return result
    else:
        print("处理失败")
        return None

def visualize_decomposition_results(original_data, meemd_result, prefix):
    """可视化分解结果"""
    components = meemd_result['final_components']
    num_components = len(components)
    
    # 创建子图
    fig, axes = plt.subplots(2, (num_components + 2) // 2, figsize=(15, 8))
    axes = axes.flatten() if num_components > 1 else [axes]
    
    # 显示原始数据
    im0 = axes[0].imshow(original_data, cmap='RdYlBu_r', aspect='auto')
    axes[0].set_title('原始数据')
    axes[0].set_xlabel('经度索引')
    axes[0].set_ylabel('纬度索引')
    plt.colorbar(im0, ax=axes[0])
    
    # 显示各个分量
    for i, component in enumerate(components):
        if i + 1 < len(axes):
            im = axes[i + 1].imshow(component, cmap='RdBu_r', aspect='auto')
            axes[i + 1].set_title(f'分量 {i + 1}')
            axes[i + 1].set_xlabel('经度索引')
            axes[i + 1].set_ylabel('纬度索引')
            plt.colorbar(im, ax=axes[i + 1])
    
    # 隐藏多余的子图
    for i in range(num_components + 1, len(axes)):
        axes[i].set_visible(False)
    
    plt.tight_layout()
    
    # 保存图片
    output_dir = Path("results")
    output_dir.mkdir(exist_ok=True)
    plt.savefig(output_dir / f"{prefix}_decomposition.png", dpi=150, bbox_inches='tight')
    print(f"可视化结果已保存: {output_dir / f'{prefix}_decomposition.png'}")
    
    plt.show()

def test_processing_pipeline():
    """测试完整的处理管道"""
    print("\n" + "=" * 50)
    print("测试3: 完整处理管道")
    print("=" * 50)
    
    if not Path("SST-V2.nc").exists():
        print("警告: SST-V2.nc 文件不存在，跳过管道测试")
        return
    
    # 配置参数
    processing_config = ProcessingConfig(
        batch_size=2,
        start_time_idx=0,
        end_time_idx=5,  # 处理前5个时间点
        save_intermediate=True,
        overwrite_cache=True
    )
    
    meemd_config = MEEMDConfig(
        trials=15,  # 进一步减少计算时间
        noise_std=0.1,
        max_imfs=5
    )
    
    # 创建处理器
    processor = SSTDataProcessor(processing_config, meemd_config)
    
    # 加载数据
    if not processor.load_data():
        return
    
    # 获取初始摘要
    initial_summary = processor.get_processing_summary()
    print(f"初始状态: {initial_summary}")
    
    # 批量处理
    print("\n开始批量处理...")
    results = processor.process_batch()
    
    # 最终摘要
    final_summary = processor.get_processing_summary()
    print(f"\n处理完成: {final_summary}")
    
    # 分析结果
    if results:
        print(f"\n成功处理了 {len(results)} 个时间点")
        
        # 统计分量数量
        component_counts = [len(r['meemd_result']['final_components']) for r in results if r is not None]
        if component_counts:
            print(f"分量数量统计: 平均={np.mean(component_counts):.1f}, 范围={min(component_counts)}-{max(component_counts)}")
        
        # 统计重构误差
        reconstruction_errors = [r['meemd_result']['reconstruction_error'] for r in results if r is not None]
        if reconstruction_errors:
            print(f"重构误差统计: 平均={np.mean(reconstruction_errors):.6f}, 标准差={np.std(reconstruction_errors):.6f}")

def main():
    """主测试函数"""
    print("MEEMD二维分解测试程序")
    print("=" * 60)
    
    # 创建必要的目录
    for dir_name in ['src', 'results', 'cache']:
        Path(dir_name).mkdir(exist_ok=True)
    
    try:
        # 测试1: 合成数据
        synthetic_result = test_synthetic_data()
        
        # 测试2: 真实数据单点处理
        real_result = test_real_sst_data()
        
        # 测试3: 完整管道
        test_processing_pipeline()
        
        print("\n" + "=" * 60)
        print("所有测试完成！")
        print("请查看 results/ 目录中的可视化结果")
        print("缓存文件保存在 cache/ 目录中")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
