"""
SST数据处理管道
支持逐时间点的二维MEEMD分解处理
"""

import numpy as np
import xarray as xr
import pandas as pd
from pathlib import Path
from typing import List, Dict, Optional, Tuple, Generator
import pickle
import os
from datetime import datetime
import logging
from dataclasses import dataclass

from meemd_2d import MEEMD2D, MEEMDConfig

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ProcessingConfig:
    """数据处理配置"""
    data_file: str = "SST-V2.nc"
    output_dir: str = "results"
    cache_dir: str = "cache"
    batch_size: int = 10          # 批处理大小
    start_time_idx: int = 0       # 开始时间索引
    end_time_idx: Optional[int] = None  # 结束时间索引
    save_intermediate: bool = True # 是否保存中间结果
    overwrite_cache: bool = False  # 是否覆盖缓存
    
class SSTDataProcessor:
    """SST数据处理器"""
    
    def __init__(self, config: ProcessingConfig = None, meemd_config: MEEMDConfig = None):
        self.config = config or ProcessingConfig()
        self.meemd_config = meemd_config or MEEMDConfig()
        self.meemd = MEEMD2D(self.meemd_config)
        
        # 创建输出目录
        self._setup_directories()
        
        # 加载数据
        self.dataset = None
        self.sst_data = None
        self.time_coords = None
        self.lat_coords = None
        self.lon_coords = None
        
    def _setup_directories(self):
        """创建必要的目录"""
        for dir_path in [self.config.output_dir, self.config.cache_dir]:
            Path(dir_path).mkdir(parents=True, exist_ok=True)
            
    def load_data(self) -> bool:
        """加载SST数据"""
        try:
            logger.info(f"加载数据文件: {self.config.data_file}")
            self.dataset = xr.open_dataset(self.config.data_file)
            
            # 提取SST数据和坐标
            self.sst_data = self.dataset['analysed_sst'].values
            self.time_coords = self.dataset['time'].values
            self.lat_coords = self.dataset['latitude'].values
            self.lon_coords = self.dataset['longitude'].values
            
            logger.info(f"数据形状: {self.sst_data.shape}")
            logger.info(f"时间范围: {self.time_coords[0]} 到 {self.time_coords[-1]}")
            logger.info(f"空间范围: 纬度 {self.lat_coords.min():.2f}-{self.lat_coords.max():.2f}, "
                       f"经度 {self.lon_coords.min():.2f}-{self.lon_coords.max():.2f}")
            
            return True
            
        except Exception as e:
            logger.error(f"数据加载失败: {e}")
            return False
    
    def get_time_slice_generator(self) -> Generator[Tuple[int, np.ndarray, pd.Timestamp], None, None]:
        """生成时间切片的生成器"""
        if self.sst_data is None:
            raise ValueError("数据未加载，请先调用load_data()")
            
        start_idx = self.config.start_time_idx
        end_idx = self.config.end_time_idx or self.sst_data.shape[0]
        
        logger.info(f"处理时间范围: 索引 {start_idx} 到 {end_idx-1}")
        
        for t_idx in range(start_idx, end_idx):
            sst_slice = self.sst_data[t_idx, :, :]
            timestamp = pd.to_datetime(self.time_coords[t_idx])
            yield t_idx, sst_slice, timestamp
    
    def process_single_time_slice(self, t_idx: int, sst_slice: np.ndarray,
                                timestamp: pd.Timestamp) -> Dict:
        """处理单个时间切片"""
        timestamp_str = pd.to_datetime(timestamp).strftime('%Y-%m-%d')
        logger.info(f"处理时间点 {t_idx}: {timestamp_str}")
        
        # 检查缓存
        cache_file = Path(self.config.cache_dir) / f"meemd_result_{t_idx:04d}.pkl"
        if cache_file.exists() and not self.config.overwrite_cache:
            logger.info(f"从缓存加载结果: {cache_file}")
            with open(cache_file, 'rb') as f:
                return pickle.load(f)
        
        # 数据预处理
        processed_slice = self._preprocess_slice(sst_slice)
        
        # 执行MEEMD分解
        try:
            meemd_result = self.meemd.decompose_2d(processed_slice)
            
            # 添加元数据
            result = {
                'time_index': t_idx,
                'timestamp': timestamp,
                'meemd_result': meemd_result,
                'original_data': sst_slice,
                'processed_data': processed_slice,
                'coordinates': {
                    'lat': self.lat_coords,
                    'lon': self.lon_coords
                }
            }
            
            # 分析分量特性
            analysis = self.meemd.analyze_components(meemd_result['final_components'])
            result['component_analysis'] = analysis
            
            # 保存到缓存
            if self.config.save_intermediate:
                with open(cache_file, 'wb') as f:
                    pickle.dump(result, f)
                logger.info(f"结果已缓存: {cache_file}")
            
            return result
            
        except Exception as e:
            logger.error(f"时间点 {t_idx} 处理失败: {e}")
            return None
    
    def _preprocess_slice(self, sst_slice: np.ndarray) -> np.ndarray:
        """预处理单个时间切片"""
        # 转换温度单位（从开尔文到摄氏度）
        processed = sst_slice - 273.15
        
        # 处理异常值
        processed = np.where(processed < -5, np.nan, processed)  # 低于-5°C的值
        processed = np.where(processed > 50, np.nan, processed)  # 高于50°C的值
        
        # 简单的缺失值插值（如果有的话）
        if np.any(np.isnan(processed)):
            # 使用邻近值填充
            mask = np.isnan(processed)
            processed[mask] = np.nanmean(processed)
            
        return processed
    
    def process_batch(self, batch_size: Optional[int] = None) -> List[Dict]:
        """批量处理时间切片"""
        batch_size = batch_size or self.config.batch_size
        results = []
        
        for t_idx, sst_slice, timestamp in self.get_time_slice_generator():
            result = self.process_single_time_slice(t_idx, sst_slice, timestamp)
            if result is not None:
                results.append(result)
            
            # 批量保存
            if len(results) >= batch_size:
                self._save_batch_results(results)
                results = []
        
        # 保存剩余结果
        if results:
            self._save_batch_results(results)
            
        return results
    
    def _save_batch_results(self, results: List[Dict]):
        """保存批量结果"""
        if not results:
            return
            
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        start_idx = results[0]['time_index']
        end_idx = results[-1]['time_index']
        
        output_file = Path(self.config.output_dir) / f"batch_results_{start_idx:04d}_{end_idx:04d}_{timestamp}.pkl"
        
        with open(output_file, 'wb') as f:
            pickle.dump(results, f)
            
        logger.info(f"批量结果已保存: {output_file} (包含 {len(results)} 个时间点)")
    
    def load_all_results(self) -> List[Dict]:
        """加载所有处理结果"""
        results = []
        cache_dir = Path(self.config.cache_dir)
        
        # 按时间索引排序加载
        cache_files = sorted(cache_dir.glob("meemd_result_*.pkl"))
        
        for cache_file in cache_files:
            try:
                with open(cache_file, 'rb') as f:
                    result = pickle.load(f)
                    results.append(result)
            except Exception as e:
                logger.warning(f"加载缓存文件失败 {cache_file}: {e}")
        
        logger.info(f"加载了 {len(results)} 个时间点的结果")
        return results
    
    def get_processing_summary(self) -> Dict:
        """获取处理摘要"""
        cache_dir = Path(self.config.cache_dir)
        cache_files = list(cache_dir.glob("meemd_result_*.pkl"))
        
        if not cache_files:
            return {"processed_count": 0, "total_count": 0}
        
        total_time_points = self.sst_data.shape[0] if self.sst_data is not None else 0
        processed_count = len(cache_files)
        
        # 获取已处理的时间索引
        processed_indices = []
        for cache_file in cache_files:
            try:
                idx = int(cache_file.stem.split('_')[-1])
                processed_indices.append(idx)
            except:
                continue
        
        return {
            "processed_count": processed_count,
            "total_count": total_time_points,
            "progress_percentage": (processed_count / total_time_points * 100) if total_time_points > 0 else 0,
            "processed_indices": sorted(processed_indices),
            "missing_indices": [i for i in range(total_time_points) if i not in processed_indices]
        }
    
    def cleanup_cache(self):
        """清理缓存文件"""
        cache_dir = Path(self.config.cache_dir)
        cache_files = list(cache_dir.glob("meemd_result_*.pkl"))
        
        for cache_file in cache_files:
            cache_file.unlink()
            
        logger.info(f"已清理 {len(cache_files)} 个缓存文件")

def main():
    """主函数 - 演示数据处理流程"""
    # 配置参数
    processing_config = ProcessingConfig(
        batch_size=5,
        start_time_idx=0,
        end_time_idx=10,  # 只处理前10个时间点作为测试
        save_intermediate=True
    )
    
    meemd_config = MEEMDConfig(
        trials=50,  # 减少计算时间
        parallel=False
    )
    
    # 创建处理器
    processor = SSTDataProcessor(processing_config, meemd_config)
    
    # 加载数据
    if not processor.load_data():
        return
    
    # 获取处理摘要
    summary = processor.get_processing_summary()
    print(f"处理摘要: {summary}")
    
    # 批量处理
    print("开始批量处理...")
    results = processor.process_batch()
    
    # 最终摘要
    final_summary = processor.get_processing_summary()
    print(f"处理完成: {final_summary}")

if __name__ == "__main__":
    main()
